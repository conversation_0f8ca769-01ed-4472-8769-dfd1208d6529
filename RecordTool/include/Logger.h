#pragma once

#include <spdlog/spdlog.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <memory>
#include <string>

/**
 * @brief 日志管理器类
 * 
 * 提供统一的日志接口，支持控制台和文件输出
 * 使用 spdlog 库实现高性能日志记录
 */
class Logger {
public:
    /**
     * @brief 日志级别枚举
     */
    enum class Level {
        TRACE = 0,
        DEBUG = 1,
        INFO = 2,
        WARN = 3,
        ERROR = 4,
        CRITICAL = 5
    };

    /**
     * @brief 获取日志器单例
     * @return Logger& 日志器引用
     */
    static Logger& getInstance();

    /**
     * @brief 初始化日志器
     * @param log_dir 日志文件目录
     * @param app_name 应用程序名称
     * @param max_file_size 单个日志文件最大大小（字节）
     * @param max_files 最大日志文件数量
     * @param console_level 控制台日志级别
     * @param file_level 文件日志级别
     * @return bool 初始化是否成功
     */
    bool initialize(const std::string& log_dir = "./logs",
                   const std::string& app_name = "RecordTool",
                   size_t max_file_size = 10 * 1024 * 1024,  // 10MB
                   size_t max_files = 5,
                   Level console_level = Level::INFO,
                   Level file_level = Level::DEBUG);

    /**
     * @brief 记录信息级别日志
     * @param message 日志消息
     */
    void info(const std::string& message);

    /**
     * @brief 记录调试级别日志
     * @param message 日志消息
     */
    void debug(const std::string& message);

    /**
     * @brief 记录警告级别日志
     * @param message 日志消息
     */
    void warn(const std::string& message);

    /**
     * @brief 记录错误级别日志
     * @param message 日志消息
     */
    void error(const std::string& message);

    /**
     * @brief 记录状态级别日志（特殊的信息日志）
     * @param message 日志消息
     */
    void status(const std::string& message);

    /**
     * @brief 记录推流状态日志
     * @param frames_sent 已发送帧数
     * @param bytes_sent 已发送字节数
     * @param bitrate_mbps 码率（Mbps）
     * @param duration_seconds 推流时长（秒）
     */
    void logStreamStatus(int64_t frames_sent, int64_t bytes_sent, 
                        double bitrate_mbps, double duration_seconds);

    /**
     * @brief 记录文件状态日志
     * @param filename 文件名
     * @param file_size 文件大小
     * @param duration_seconds 文件时长（秒）
     * @param action 操作类型（如"创建"、"完成"等）
     */
    void logFileStatus(const std::string& filename, int64_t file_size,
                      double duration_seconds, const std::string& action);

    /**
     * @brief 设置日志级别
     * @param level 日志级别
     */
    void setLevel(Level level);

    /**
     * @brief 刷新日志缓冲区
     */
    void flush();

private:
    Logger() = default;
    ~Logger() = default;
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;

    /**
     * @brief 转换日志级别
     * @param level 自定义日志级别
     * @return spdlog::level::level_enum spdlog日志级别
     */
    spdlog::level::level_enum toSpdlogLevel(Level level);

    /**
     * @brief 格式化字节数
     * @param bytes 字节数
     * @return std::string 格式化后的字符串
     */
    std::string formatBytes(int64_t bytes);

    /**
     * @brief 格式化时长
     * @param seconds 秒数
     * @return std::string 格式化后的字符串
     */
    std::string formatDuration(double seconds);

private:
    std::shared_ptr<spdlog::logger> logger_;
    bool initialized_ = false;
};
