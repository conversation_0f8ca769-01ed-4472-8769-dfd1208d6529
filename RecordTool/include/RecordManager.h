#ifndef RECORD_MANAGER_H
#define RECORD_MANAGER_H

#include "ScreenRecorder.h"
#include "StreamPublisher.h"

#include <string>
#include <memory>
#include <atomic>
#include <thread>
#include <chrono>
#include <vector>
#include <functional>
#include <mutex>

/**
 * @brief 录制管理器类
 *
 * 统一管理屏幕录制和推流功能
 * 支持30分钟分段录制和实时推流
 */
class RecordManager {
public:
    /**
     * @brief 管理器配置结构体
     */
    struct ManagerConfig {
        // 录制配置
        std::string output_dir = "./recordings/";  // 录制文件输出目录
        std::string file_prefix = "screen_record"; // 文件名前缀
        int segment_duration = 1800;               // 分段时长（秒，默认30分钟）

        // 推流配置
        std::string rtmp_url;                      // RTMP推流地址
        bool enable_streaming = true;              // 是否启用推流

        // 视频参数
        int width = 1920;                          // 视频宽度
        int height = 1080;                         // 视频高度
        int fps = 30;                              // 帧率
        int record_bitrate = 2000000;              // 录制码率
        int stream_bitrate = 1500000;              // 推流码率

        // 其他选项
        bool auto_restart = true;                  // 自动重启
        int max_retry_count = 3;                   // 最大重试次数
        std::string log_level = "info";            // 日志级别
    };

    /**
     * @brief 录制状态枚举
     */
    enum class RecordStatus {
        STOPPED,        // 已停止
        STARTING,       // 启动中
        RECORDING,      // 录制中
        STOPPING,       // 停止中
        ERROR           // 错误状态
    };

    /**
     * @brief 构造函数
     * @param config 管理器配置
     */
    explicit RecordManager(const ManagerConfig& config);

    /**
     * @brief 析构函数
     */
    ~RecordManager();

    /**
     * @brief 初始化管理器
     * @return 成功返回true，失败返回false
     */
    bool initialize();

    /**
     * @brief 开始录制和推流
     * @return 成功返回true，失败返回false
     */
    bool start();

    /**
     * @brief 停止录制和推流
     */
    void stop();

    /**
     * @brief 暂停录制（保持推流）
     */
    void pause();

    /**
     * @brief 恢复录制
     */
    void resume();

    /**
     * @brief 获取当前状态
     * @return 当前录制状态
     */
    RecordStatus getStatus() const { return status_.load(); }

    /**
     * @brief 获取当前录制文件信息
     * @param current_file 当前录制文件路径
     * @param file_duration 当前文件录制时长
     * @param total_duration 总录制时长
     */
    void getCurrentFileInfo(std::string& current_file, double& file_duration, double& total_duration) const;

    /**
     * @brief 获取推流统计信息
     * @param is_streaming 是否正在推流
     * @param frames_sent 已发送帧数
     * @param bytes_sent 已发送字节数
     * @param stream_duration 推流时长
     */
    void getStreamInfo(bool& is_streaming, int64_t& frames_sent, int64_t& bytes_sent, double& stream_duration) const;

    /**
     * @brief 设置推流质量
     * @param quality 质量等级 (1-10)
     */
    void setStreamQuality(int quality);

    /**
     * @brief 获取已录制的文件列表
     * @return 文件路径列表
     */
    std::vector<std::string> getRecordedFiles() const;

    /**
     * @brief 设置错误回调函数
     * @param callback 错误回调函数
     */
    void setErrorCallback(std::function<void(const std::string&)> callback);

private:
    ManagerConfig config_;                          // 配置

    // 核心组件
    std::unique_ptr<ScreenRecorder> recorder_;      // 屏幕录制器
    std::unique_ptr<StreamPublisher> publisher_;    // 推流器

    // 状态控制
    std::atomic<RecordStatus> status_;              // 当前状态
    std::atomic<bool> running_;                     // 运行状态
    std::atomic<bool> paused_;                      // 暂停状态
    std::thread manager_thread_;                    // 管理线程
    mutable std::mutex mutex_;                      // 互斥锁

    // 文件管理
    std::string current_file_;                      // 当前录制文件
    std::vector<std::string> recorded_files_;       // 已录制文件列表
    std::chrono::steady_clock::time_point segment_start_time_;  // 分段开始时间
    std::chrono::steady_clock::time_point total_start_time_;    // 总开始时间

    // 错误处理
    std::function<void(const std::string&)> error_callback_;    // 错误回调
    int retry_count_;                               // 重试计数

    /**
     * @brief 管理线程函数
     */
    void managerThread();

    /**
     * @brief 生成新的录制文件名
     * @return 文件路径
     */
    std::string generateFileName() const;

    /**
     * @brief 检查是否需要切换文件
     * @return 需要切换返回true
     */
    bool shouldSwitchFile() const;

    /**
     * @brief 切换到新的录制文件
     * @return 成功返回true，失败返回false
     */
    bool switchToNewFile();

    /**
     * @brief 创建输出目录
     * @return 成功返回true，失败返回false
     */
    bool createOutputDirectory() const;

    /**
     * @brief 处理错误
     * @param error_msg 错误消息
     */
    void handleError(const std::string& error_msg);

    /**
     * @brief 清理资源
     */
    void cleanup();
};

#endif // RECORD_MANAGER_H
