# 屏幕录制和推流工具

基于C++ FFmpeg库开发的高性能屏幕录制和实时推流工具，支持30分钟自动分段录制和RTMP实时推流。

## 功能特性

- ✅ **屏幕录制**: 支持X11屏幕捕获，H.264编码
- ✅ **自动分段**: 30分钟自动切换录制文件，避免单文件过大
- ✅ **实时推流**: 支持RTMP协议推流，可在外部实时观看
- ✅ **双路输出**: 同时录制本地文件和推流，互不影响
- ✅ **质量控制**: 可动态调整推流质量和码率
- ✅ **错误恢复**: 自动重试机制，提高稳定性
- ✅ **状态监控**: 实时显示录制和推流状态信息

## 系统要求

- Linux系统（支持X11）
- FFmpeg开发库 (libavcodec, libavformat, libavutil, libavdevice等)
- X11开发库 (libX11, libXext, libXfixes)
- CMake 3.16+
- GCC/Clang支持C++17

## 安装依赖

### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install \
    libavcodec-dev libavformat-dev libavutil-dev \
    libavdevice-dev libavfilter-dev libswscale-dev \
    libswresample-dev libx11-dev libxext-dev \
    libxfixes-dev cmake build-essential
```

### CentOS/RHEL
```bash
sudo yum install epel-release
sudo yum install \
    ffmpeg-devel libX11-devel libXext-devel \
    libXfixes-devel cmake gcc-c++
```

### Fedora
```bash
sudo dnf install \
    ffmpeg-devel libX11-devel libXext-devel \
    libXfixes-devel cmake gcc-c++
```

## 编译安装

1. **克隆或下载源码**
```bash
cd RecordTool
```

2. **运行构建脚本**
```bash
./build.sh
```

3. **手动构建（可选）**
```bash
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
```

## 使用方法

### 基本用法

1. **仅录制到本地文件**
```bash
./RecordTool
```

2. **录制并推流**
```bash
./RecordTool -r rtmp://live.example.com/live/your_stream_key
```

3. **自定义参数**
```bash
./RecordTool -o /path/to/recordings -d 600 -w 1280 -h 720 -f 25
```

### 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `-h, --help` | 显示帮助信息 | - |
| `-o, --output DIR` | 录制文件输出目录 | `./recordings/` |
| `-p, --prefix PREFIX` | 文件名前缀 | `screen_record` |
| `-d, --duration SECONDS` | 分段时长（秒） | `1800` (30分钟) |
| `-r, --rtmp URL` | RTMP推流地址 | - |
| `-w, --width WIDTH` | 视频宽度 | `1920` |
| `--height HEIGHT` | 视频高度 | `1080` |
| `-f, --fps FPS` | 帧率 | `30` |
| `--record-bitrate RATE` | 录制码率 | `2000000` |
| `--stream-bitrate RATE` | 推流码率 | `1500000` |
| `--no-stream` | 禁用推流功能 | - |

### 使用示例

1. **算法测试录制**
```bash
# 录制算法测试过程，每30分钟一个文件
./RecordTool -o /data/algorithm_tests -p algo_test
```

2. **实时演示推流**
```bash
# 推流到直播平台进行实时演示
./RecordTool -r rtmp://live.bilibili.com/live/YOUR_KEY \
             -w 1280 -h 720 --stream-bitrate 1000000
```

3. **高质量录制**
```bash
# 高码率录制，适合后期处理
./RecordTool --record-bitrate 5000000 -f 60 --no-stream
```

## 输出文件

录制的文件按时间戳命名，格式为：
```
{prefix}_{YYYYMMDD_HHMMSS}.mp4
```

例如：
- `screen_record_20240627_143022.mp4`
- `algo_test_20240627_143022.mp4`

## 推流设置

### 常见推流平台

1. **Bilibili直播**
```bash
./RecordTool -r rtmp://live-push.bilivideo.com/live-bvc/YOUR_KEY
```

2. **YouTube Live**
```bash
./RecordTool -r rtmp://a.rtmp.youtube.com/live2/YOUR_KEY
```

3. **自建RTMP服务器**
```bash
./RecordTool -r rtmp://your-server.com:1935/live/stream
```

### 网页观看

推流成功后，可以通过以下方式观看：

1. **VLC播放器**
```bash
vlc rtmp://your-server.com:1935/live/stream
```

2. **网页播放器** (需要配置RTMP服务器支持HLS/DASH)

3. **OBS Studio** 等直播软件

## 性能优化

1. **CPU优化**: 使用硬件编码器（如果支持）
2. **网络优化**: 根据网络带宽调整推流码率
3. **存储优化**: 使用SSD存储提高写入性能
4. **系统优化**: 关闭不必要的后台程序

## 故障排除

### 常见问题

1. **无法捕获屏幕**
   - 检查X11权限：`xhost +local:`
   - 确认DISPLAY环境变量：`echo $DISPLAY`

2. **推流连接失败**
   - 检查网络连接和防火墙设置
   - 验证RTMP地址和密钥是否正确

3. **编码性能问题**
   - 降低分辨率或帧率
   - 调整编码预设（preset）

4. **文件权限问题**
   - 确保输出目录有写入权限
   - 检查磁盘空间是否充足

### 日志分析

程序运行时会输出详细的状态信息，包括：
- 录制状态和文件信息
- 推流状态和统计数据
- 错误信息和重试情况

## 开发说明

### 项目结构
```
RecordTool/
├── include/           # 头文件
│   ├── ScreenRecorder.h
│   ├── StreamPublisher.h
│   └── RecordManager.h
├── src/              # 源文件
│   ├── ScreenRecorder.cpp
│   ├── StreamPublisher.cpp
│   ├── RecordManager.cpp
│   └── main.cpp
├── CMakeLists.txt    # 构建配置
├── build.sh          # 构建脚本
└── README.md         # 说明文档
```

### 核心组件

1. **ScreenRecorder**: 屏幕捕获和录制
2. **StreamPublisher**: 实时推流
3. **RecordManager**: 统一管理和调度

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至开发团队
