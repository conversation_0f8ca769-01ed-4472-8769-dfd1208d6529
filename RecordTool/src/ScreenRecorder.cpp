#include "../include/ScreenRecorder.h"
#include "../include/Logger.h"
#include <cstring>
#include <thread>
#include <chrono>
#include <mutex>
#include <algorithm>

// FFmpeg headers
extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavdevice/avdevice.h>
#include <libswscale/swscale.h>
#include <libavutil/avutil.h>
#include <libavutil/imgutils.h>
}

// X11 headers
#include <X11/Xlib.h>
#include <X11/Xutil.h>

// 辅助函数：获取FFmpeg错误字符串
static std::string av_error_string(int error_code) {
    char errbuf[AV_ERROR_MAX_STRING_SIZE];
    av_strerror(error_code, errbuf, AV_ERROR_MAX_STRING_SIZE);
    return std::string(errbuf);
}

// X11错误处理器
static bool x11_error_occurred = false;
static int x11_error_handler(Display* display, XErrorEvent* error) {
    x11_error_occurred = true;
    return 0; // 忽略错误
}

ScreenRecorder::ScreenRecorder(const RecordConfig& config)
    : config_(config)
    , input_format_ctx_(nullptr)
    , output_format_ctx_(nullptr)
    , codec_ctx_(nullptr)
    , video_stream_(nullptr)
    , sws_ctx_(nullptr)
    , input_frame_(nullptr)
    , output_frame_(nullptr)
    , packet_(nullptr)
    , recording_(false)
    , initialized_(false)
    , start_time_(0)
    , frame_count_(0)
    , display_(nullptr)
    , root_window_(0)
    , x_image_(nullptr)
    , screen_width_(0)
    , screen_height_(0)
    , use_ffmpeg_capture_(false)
    , input_codec_ctx_(nullptr) {
}

ScreenRecorder::~ScreenRecorder() {
    stopRecording();
    cleanup();
}

bool ScreenRecorder::initialize() {
    std::lock_guard<std::mutex> lock(mutex_);

    if (initialized_.load()) {
        return true;
    }

    // 注册所有设备和格式
    avdevice_register_all();

    // 初始化输入设备
    if (!initializeInput()) {
        Logger::getInstance().error("初始化输入设备失败");
        return false;
    }

    // 初始化编码器
    if (!initializeEncoder()) {
        Logger::getInstance().error("初始化编码器失败");
        cleanup();
        return false;
    }

    // 分配帧和数据包
    input_frame_ = av_frame_alloc();
    output_frame_ = av_frame_alloc();
    packet_ = av_packet_alloc();

    if (!input_frame_ || !output_frame_ || !packet_) {
        Logger::getInstance().error("分配帧或数据包失败");
        cleanup();
        return false;
    }

    // 设置输出帧参数
    output_frame_->format = codec_ctx_->pix_fmt;
    output_frame_->width = config_.width;
    output_frame_->height = config_.height;

    // 分配输出帧缓冲区
    int ret = av_frame_get_buffer(output_frame_, 32);
    if (ret < 0) {
        Logger::getInstance().error(std::string("分配输出帧缓冲区失败: ") + av_error_string(ret));
        cleanup();
        return false;
    }

    initialized_.store(true);
    Logger::getInstance().info("屏幕录制器初始化成功");
    return true;
}

bool ScreenRecorder::startRecording(const std::string& output_file) {
    if (!initialized_.load()) {
        Logger::getInstance().error("录制器未初始化");
        return false;
    }

    if (recording_.load()) {
        Logger::getInstance().error("已经在录制中");
        return false;
    }

    // 初始化输出文件
    if (!initializeOutput(output_file)) {
        Logger::getInstance().error(std::string("初始化输出文件失败: ") + output_file);
        return false;
    }

    // 写入文件头
    int ret = avformat_write_header(output_format_ctx_, nullptr);
    if (ret < 0) {
        Logger::getInstance().error(std::string("写入文件头失败: ") + av_error_string(ret));
        return false;
    }

    recording_.store(true);
    start_time_ = av_gettime();
    frame_count_ = 0; // 重置帧计数器

    // 启动录制线程
    record_thread_ = std::thread(&ScreenRecorder::recordingThread, this);

    Logger::getInstance().info(std::string("开始录制到文件: ") + output_file);
    return true;
}

void ScreenRecorder::stopRecording() {
    if (!recording_.load()) {
        return;
    }

    recording_.store(false);

    // 等待录制线程结束
    if (record_thread_.joinable()) {
        record_thread_.join();
    }

    // 写入文件尾
    if (output_format_ctx_) {
        av_write_trailer(output_format_ctx_);
        avio_closep(&output_format_ctx_->pb);
        avformat_free_context(output_format_ctx_);
        output_format_ctx_ = nullptr;
        video_stream_ = nullptr;
    }

    Logger::getInstance().info("录制已停止");
}

bool ScreenRecorder::captureFrame(AVFrame*& frame) {
    if (!initialized_.load()) {
        return false;
    }

    // 确保帧数据可写
    int ret = av_frame_make_writable(output_frame_);
    if (ret < 0) {
        Logger::getInstance().error(std::string("无法使帧可写: ") + av_error_string(ret));
        return false;
    }

    // 设置正确的时间戳 - 使用编码器的时间基准
    output_frame_->pts = frame_count_;

    // 尝试捕获真实屏幕数据
    static bool screen_capture_warned = false;
    bool capture_success = false;

    if (use_ffmpeg_capture_) {
        capture_success = captureScreenFFmpeg();
    } else {
        capture_success = captureScreen();
    }

    if (!capture_success) {
        // 如果屏幕捕获失败，使用测试图像
        if (!screen_capture_warned) {
            Logger::getInstance().warn("注意: 屏幕捕获失败，使用测试图案模式");
            Logger::getInstance().warn("要启用真实屏幕录制，请确保:");
            Logger::getInstance().warn("1. 在X11会话中运行（不是Wayland）");
            Logger::getInstance().warn("2. 运行 'xhost +local:' 设置X11权限");
            Logger::getInstance().warn("3. 确保DISPLAY环境变量正确设置");
            Logger::getInstance().warn("4. 在图形界面环境中运行程序");
            screen_capture_warned = true;
        }
        generateTestFrame();
    }

    frame_count_++;
    frame = output_frame_;
    return true;
}

void ScreenRecorder::generateTestFrame() {
    // 生成一个动态的测试图像
    // Y分量（亮度）
    for (int y = 0; y < config_.height; y++) {
        uint8_t* row = output_frame_->data[0] + y * output_frame_->linesize[0];
        for (int x = 0; x < config_.width; x++) {
            // 创建移动的渐变图案
            int pattern = (x + y + frame_count_ * 2) % 256;
            row[x] = pattern;
        }
    }

    // U分量（色度）
    for (int y = 0; y < config_.height / 2; y++) {
        uint8_t* row = output_frame_->data[1] + y * output_frame_->linesize[1];
        for (int x = 0; x < config_.width / 2; x++) {
            // 添加一些颜色变化
            int pattern = (x * 2 + frame_count_) % 256;
            row[x] = 128 + (pattern - 128) / 4; // 轻微的色度变化
        }
    }

    // V分量（色度）
    for (int y = 0; y < config_.height / 2; y++) {
        uint8_t* row = output_frame_->data[2] + y * output_frame_->linesize[2];
        for (int x = 0; x < config_.width / 2; x++) {
            // 添加一些颜色变化
            int pattern = (y * 2 + frame_count_) % 256;
            row[x] = 128 + (pattern - 128) / 4; // 轻微的色度变化
        }
    }
}

double ScreenRecorder::getRecordingDuration() const {
    if (!recording_.load() || start_time_ == 0) {
        return 0.0;
    }

    int64_t current_time = av_gettime();
    return (current_time - start_time_) / 1000000.0; // 转换为秒
}

void ScreenRecorder::recordingThread() {
    Logger::getInstance().info("录制线程已启动");

    auto frame_duration = std::chrono::milliseconds(1000 / config_.fps);
    auto next_frame_time = std::chrono::steady_clock::now();

    while (recording_.load()) {
        AVFrame* frame = nullptr;
        if (captureFrame(frame) && frame) {
            if (!encodeAndWriteFrame(frame)) {
                Logger::getInstance().error("编码和写入帧失败");
                break;
            }
        }

        // 精确的帧率控制
        next_frame_time += frame_duration;
        std::this_thread::sleep_until(next_frame_time);
    }

    Logger::getInstance().info("录制线程已结束");
}

bool ScreenRecorder::initializeInput() {
    Logger::getInstance().info("初始化屏幕捕获...");

    // 首先尝试FFmpeg的x11grab
    if (initializeFFmpegCapture()) {
        Logger::getInstance().info("使用FFmpeg x11grab屏幕捕获");
        return true;
    }

    // 如果FFmpeg捕获失败，尝试直接X11
    if (initializeX11()) {
        Logger::getInstance().info("使用直接X11屏幕捕获");
        return true;
    }

    Logger::getInstance().warn("屏幕捕获初始化失败，将使用测试模式");
    return true; // 仍然返回true，使用测试模式
}

bool ScreenRecorder::initializeOutput(const std::string& output_file) {
    // 分配输出格式上下文
    int ret = avformat_alloc_output_context2(&output_format_ctx_, nullptr, nullptr, output_file.c_str());
    if (ret < 0) {
        Logger::getInstance().error(std::string("分配输出格式上下文失败: ") + av_error_string(ret));
        return false;
    }

    // 创建视频流
    video_stream_ = avformat_new_stream(output_format_ctx_, nullptr);
    if (!video_stream_) {
        Logger::getInstance().error("创建视频流失败");
        return false;
    }

    // 复制编码器参数到流
    ret = avcodec_parameters_from_context(video_stream_->codecpar, codec_ctx_);
    if (ret < 0) {
        Logger::getInstance().error(std::string("复制编码器参数失败: ") + av_error_string(ret));
        return false;
    }

    video_stream_->time_base = codec_ctx_->time_base;

    // 打开输出文件
    if (!(output_format_ctx_->oformat->flags & AVFMT_NOFILE)) {
        ret = avio_open(&output_format_ctx_->pb, output_file.c_str(), AVIO_FLAG_WRITE);
        if (ret < 0) {
            Logger::getInstance().error(std::string("打开输出文件失败: ") + av_error_string(ret));
            return false;
        }
    }

    return true;
}

bool ScreenRecorder::initializeEncoder() {
    // 查找编码器
    const AVCodec* codec = avcodec_find_encoder_by_name(config_.codec.c_str());
    if (!codec) {
        Logger::getInstance().error(std::string("找不到编码器: ") + config_.codec);
        return false;
    }

    // 分配编码器上下文
    codec_ctx_ = avcodec_alloc_context3(codec);
    if (!codec_ctx_) {
        Logger::getInstance().error("分配编码器上下文失败");
        return false;
    }

    // 设置编码器参数
    codec_ctx_->bit_rate = config_.bitrate;
    codec_ctx_->width = config_.width;
    codec_ctx_->height = config_.height;
    codec_ctx_->time_base = {1, config_.fps};
    codec_ctx_->framerate = {config_.fps, 1};
    codec_ctx_->gop_size = config_.fps; // I帧间隔
    codec_ctx_->max_b_frames = 0; // 减少B帧以避免时间戳问题
    codec_ctx_->pix_fmt = AV_PIX_FMT_YUV420P;

    // 设置编码选项以避免时间戳问题
    codec_ctx_->flags |= AV_CODEC_FLAG_GLOBAL_HEADER;

    // H.264特定设置
    if (codec->id == AV_CODEC_ID_H264) {
        av_opt_set(codec_ctx_->priv_data, "preset", "medium", 0);
        av_opt_set(codec_ctx_->priv_data, "crf", "23", 0);
    }

    // 打开编码器
    int ret = avcodec_open2(codec_ctx_, codec, nullptr);
    if (ret < 0) {
        Logger::getInstance().error(std::string("打开编码器失败: ") + av_error_string(ret));
        return false;
    }

    return true;
}

void ScreenRecorder::cleanup() {
    // 清理X11资源
    cleanupX11();

    if (sws_ctx_) {
        sws_freeContext(sws_ctx_);
        sws_ctx_ = nullptr;
    }

    if (input_frame_) {
        av_frame_free(&input_frame_);
    }

    if (output_frame_) {
        av_frame_free(&output_frame_);
    }

    if (packet_) {
        av_packet_free(&packet_);
    }

    if (codec_ctx_) {
        avcodec_free_context(&codec_ctx_);
    }

    if (input_format_ctx_) {
        avformat_close_input(&input_format_ctx_);
    }

    if (output_format_ctx_) {
        if (output_format_ctx_->pb) {
            avio_closep(&output_format_ctx_->pb);
        }
        avformat_free_context(output_format_ctx_);
        output_format_ctx_ = nullptr;
    }
}

bool ScreenRecorder::encodeAndWriteFrame(AVFrame* frame) {
    // 发送帧到编码器
    int ret = avcodec_send_frame(codec_ctx_, frame);
    if (ret < 0) {
        Logger::getInstance().error(std::string("发送帧到编码器失败: ") + av_error_string(ret));
        return false;
    }

    // 接收编码后的数据包
    while (ret >= 0) {
        ret = avcodec_receive_packet(codec_ctx_, packet_);
        if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
            break;
        } else if (ret < 0) {
            Logger::getInstance().error(std::string("接收编码数据包失败: ") + av_error_string(ret));
            return false;
        }

        // 设置数据包的流索引
        packet_->stream_index = video_stream_->index;

        // 转换时间戳
        av_packet_rescale_ts(packet_, codec_ctx_->time_base, video_stream_->time_base);

        // 写入数据包到文件
        ret = av_interleaved_write_frame(output_format_ctx_, packet_);
        av_packet_unref(packet_);

        if (ret < 0) {
            Logger::getInstance().error(std::string("写入数据包失败: ") + av_error_string(ret));
            return false;
        }
    }

    return true;
}

bool ScreenRecorder::initializeX11() {
    // 初始化X11线程支持
    if (!XInitThreads()) {
        Logger::getInstance().error("无法初始化X11线程支持");
        return false;
    }

    // 设置X11错误处理器
    XSetErrorHandler(x11_error_handler);
    x11_error_occurred = false;

    // 获取DISPLAY环境变量
    const char* display_env = getenv("DISPLAY");

    // 打开X11显示器
    display_ = XOpenDisplay(display_env);
    if (!display_) {
        Logger::getInstance().error(std::string("无法打开X11显示器") + (display_env ? display_env : "(默认)"));
        return false;
    }

    // 获取默认屏幕
    int screen = DefaultScreen(display_);
    root_window_ = RootWindow(display_, screen);

    // 获取屏幕尺寸
    screen_width_ = DisplayWidth(display_, screen);
    screen_height_ = DisplayHeight(display_, screen);

    Logger::getInstance().info(std::string("屏幕尺寸: ") + std::to_string(screen_width_) + "x" + std::to_string(screen_height_));

    // 如果配置的尺寸超过屏幕尺寸，调整为屏幕尺寸
    if (config_.width > screen_width_) {
        config_.width = screen_width_;
    }
    if (config_.height > screen_height_) {
        config_.height = screen_height_;
    }

    Logger::getInstance().info(std::string("录制尺寸: ") + std::to_string(config_.width) + "x" + std::to_string(config_.height));

    return true;
}

bool ScreenRecorder::captureScreen() {
    if (!display_) {
        return false;
    }

    // 重置错误标志
    x11_error_occurred = false;

    // 确保捕获区域在屏幕范围内
    int capture_width = std::min(config_.width, screen_width_);
    int capture_height = std::min(config_.height, screen_height_);

    // 捕获屏幕图像
    x_image_ = XGetImage(display_, root_window_, 0, 0, capture_width, capture_height, AllPlanes, ZPixmap);

    // 同步X11操作，确保错误被处理
    XSync(display_, False);

    if (!x_image_ || x11_error_occurred) {
        if (x_image_) {
            XDestroyImage(x_image_);
            x_image_ = nullptr;
        }
        return false;
    }

    // 转换图像格式从RGB到YUV420P
    convertRGBToYUV();

    // 释放X11图像
    XDestroyImage(x_image_);
    x_image_ = nullptr;

    return true;
}

void ScreenRecorder::convertRGBToYUV() {
    if (!x_image_ || !output_frame_) {
        return;
    }

    // 使用实际图像尺寸
    int img_width = x_image_->width;
    int img_height = x_image_->height;

    // 简化的RGB到YUV转换
    // 注意：这是一个基本实现，实际应用中可能需要更精确的颜色空间转换

    for (int y = 0; y < std::min(img_height, config_.height); y++) {
        for (int x = 0; x < std::min(img_width, config_.width); x++) {
            // 获取RGB像素值
            unsigned long pixel = XGetPixel(x_image_, x, y);

            // 提取RGB分量（假设32位BGRA格式）
            int b = (pixel >> 0) & 0xFF;
            int g = (pixel >> 8) & 0xFF;
            int r = (pixel >> 16) & 0xFF;

            // RGB到YUV转换（ITU-R BT.601标准）
            int Y = (int)(0.299 * r + 0.587 * g + 0.114 * b);
            int U = (int)(-0.169 * r - 0.331 * g + 0.500 * b + 128);
            int V = (int)(0.500 * r - 0.419 * g - 0.081 * b + 128);

            // 限制范围
            Y = std::max(0, std::min(255, Y));
            U = std::max(0, std::min(255, U));
            V = std::max(0, std::min(255, V));

            // 设置Y分量
            output_frame_->data[0][y * output_frame_->linesize[0] + x] = Y;

            // 设置UV分量（4:2:0子采样）
            if (y % 2 == 0 && x % 2 == 0) {
                int uv_y = y / 2;
                int uv_x = x / 2;
                output_frame_->data[1][uv_y * output_frame_->linesize[1] + uv_x] = U;
                output_frame_->data[2][uv_y * output_frame_->linesize[2] + uv_x] = V;
            }
        }
    }
}

void ScreenRecorder::cleanupX11() {
    if (x_image_) {
        XDestroyImage(x_image_);
        x_image_ = nullptr;
    }

    if (display_) {
        XCloseDisplay(display_);
        display_ = nullptr;
    }

    if (input_codec_ctx_) {
        avcodec_free_context(&input_codec_ctx_);
    }
}

bool ScreenRecorder::initializeFFmpegCapture() {
    Logger::getInstance().info("尝试初始化FFmpeg x11grab...");

    // 查找x11grab输入格式
    AVInputFormat* input_format = const_cast<AVInputFormat*>(av_find_input_format("x11grab"));
    if (!input_format) {
        Logger::getInstance().warn("x11grab输入格式不可用");
        return false;
    }

    // 分配输入格式上下文
    input_format_ctx_ = avformat_alloc_context();
    if (!input_format_ctx_) {
        Logger::getInstance().error("分配输入格式上下文失败");
        return false;
    }

    // 设置输入选项
    AVDictionary* options = nullptr;
    av_dict_set(&options, "video_size", (std::to_string(config_.width) + "x" + std::to_string(config_.height)).c_str(), 0);
    av_dict_set(&options, "framerate", std::to_string(config_.fps).c_str(), 0);
    av_dict_set(&options, "show_region", "1", 0);

    // 获取DISPLAY环境变量，如果没有设置则使用默认值
    const char* display_env = getenv("DISPLAY");
    std::string display_str = display_env ? display_env : ":0.0";

    // 尝试打开输入设备
    int ret = avformat_open_input(&input_format_ctx_, display_str.c_str(), input_format, &options);
    av_dict_free(&options);

    if (ret < 0) {
        Logger::getInstance().error(std::string("FFmpeg x11grab打开失败: ") + av_error_string(ret));
        avformat_free_context(input_format_ctx_);
        input_format_ctx_ = nullptr;
        return false;
    }

    // 查找流信息
    ret = avformat_find_stream_info(input_format_ctx_, nullptr);
    if (ret < 0) {
        Logger::getInstance().error(std::string("查找流信息失败: ") + av_error_string(ret));
        avformat_close_input(&input_format_ctx_);
        return false;
    }

    // 查找视频流并设置解码器
    for (unsigned int i = 0; i < input_format_ctx_->nb_streams; i++) {
        if (input_format_ctx_->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            const AVCodec* decoder = avcodec_find_decoder(input_format_ctx_->streams[i]->codecpar->codec_id);
            if (!decoder) {
                Logger::getInstance().error("找不到解码器");
                continue;
            }

            input_codec_ctx_ = avcodec_alloc_context3(decoder);
            if (!input_codec_ctx_) {
                Logger::getInstance().error("分配解码器上下文失败");
                continue;
            }

            ret = avcodec_parameters_to_context(input_codec_ctx_, input_format_ctx_->streams[i]->codecpar);
            if (ret < 0) {
                Logger::getInstance().error(std::string("复制解码器参数失败: ") + av_error_string(ret));
                avcodec_free_context(&input_codec_ctx_);
                continue;
            }

            ret = avcodec_open2(input_codec_ctx_, decoder, nullptr);
            if (ret < 0) {
                Logger::getInstance().error(std::string("打开解码器失败: ") + av_error_string(ret));
                avcodec_free_context(&input_codec_ctx_);
                continue;
            }

            use_ffmpeg_capture_ = true;
            Logger::getInstance().info("FFmpeg x11grab初始化成功");
            return true;
        }
    }

    avformat_close_input(&input_format_ctx_);
    return false;
}

bool ScreenRecorder::captureScreenFFmpeg() {
    std::lock_guard<std::mutex> lock(mutex_);

    if (!input_format_ctx_ || !input_codec_ctx_) {
        return false;
    }

    // 从输入设备读取帧
    int ret = av_read_frame(input_format_ctx_, packet_);
    if (ret < 0) {
        if (ret == AVERROR(EAGAIN)) {
            return false; // 暂时没有数据
        }
        Logger::getInstance().error(std::string("读取FFmpeg数据包失败: ") + av_error_string(ret));
        return false;
    }

    // 验证数据包
    if (!packet_->data || packet_->size <= 0) {
        Logger::getInstance().error("接收到无效的数据包");
        av_packet_unref(packet_);
        return false;
    }

    // 发送数据包到解码器
    ret = avcodec_send_packet(input_codec_ctx_, packet_);
    if (ret < 0) {
        av_packet_unref(packet_);
        return false;
    }

    // 接收解码后的帧
    ret = avcodec_receive_frame(input_codec_ctx_, input_frame_);
    av_packet_unref(packet_);

    if (ret < 0) {
        if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
            return false;
        }
        return false;
    }

    // 图像格式转换
    if (!sws_ctx_) {
        // 验证输入参数
        if (input_frame_->width <= 0 || input_frame_->height <= 0 ||
            config_.width <= 0 || config_.height <= 0) {
            Logger::getInstance().error("图像尺寸参数无效: input=" + std::to_string(input_frame_->width) + "x" + std::to_string(input_frame_->height)
                      + " output=" + std::to_string(config_.width) + "x" + std::to_string(config_.height));
            return false;
        }

        sws_ctx_ = sws_getContext(
            input_frame_->width, input_frame_->height, (AVPixelFormat)input_frame_->format,
            config_.width, config_.height, codec_ctx_->pix_fmt,
            SWS_BICUBIC, nullptr, nullptr, nullptr
        );

        if (!sws_ctx_) {
            Logger::getInstance().error("创建图像转换上下文失败");
            return false;
        }
    }

    // 验证输入帧数据
    if (!input_frame_->data[0] || input_frame_->width <= 0 || input_frame_->height <= 0) {
        Logger::getInstance().error("输入帧数据无效");
        return false;
    }

    // 验证输出帧数据
    if (!output_frame_->data[0] || output_frame_->width <= 0 || output_frame_->height <= 0) {
        Logger::getInstance().error("输出帧数据无效");
        return false;
    }

    // 转换图像格式
    int scale_ret = sws_scale(sws_ctx_,
                              input_frame_->data, input_frame_->linesize, 0, input_frame_->height,
                              output_frame_->data, output_frame_->linesize);

    if (scale_ret < 0) {
        Logger::getInstance().error(std::string("图像格式转换失败: ") + std::to_string(scale_ret));
        return false;
    }

    return true;
}