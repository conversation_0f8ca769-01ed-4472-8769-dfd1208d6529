#include "RecordManager.h"
#include "Logger.h"
#include <iomanip>
#include <sstream>
#include <filesystem>
#include <ctime>

RecordManager::RecordManager(const ManagerConfig& config)
    : config_(config)
    , status_(RecordStatus::STOPPED)
    , running_(false)
    , paused_(false)
    , retry_count_(0) {
}

RecordManager::~RecordManager() {
    stop();
    cleanup();
}

bool RecordManager::initialize() {
    std::lock_guard<std::mutex> lock(mutex_);

    // 创建输出目录
    if (!createOutputDirectory()) {
        handleError("创建输出目录失败: " + config_.output_dir);
        return false;
    }

    // 初始化屏幕录制器
    ScreenRecorder::RecordConfig record_config;
    record_config.width = config_.width;
    record_config.height = config_.height;
    record_config.fps = config_.fps;
    record_config.bitrate = config_.record_bitrate;

    recorder_ = std::make_unique<ScreenRecorder>(record_config);
    if (!recorder_->initialize()) {
        handleError("初始化屏幕录制器失败");
        return false;
    }

    // 初始化推流器（如果启用）
    if (config_.enable_streaming && !config_.rtmp_url.empty()) {
        StreamPublisher::StreamConfig stream_config;
        stream_config.rtmp_url = config_.rtmp_url;
        stream_config.width = config_.width;
        stream_config.height = config_.height;
        stream_config.fps = config_.fps;
        stream_config.bitrate = config_.stream_bitrate;

        publisher_ = std::make_unique<StreamPublisher>(stream_config);
        if (!publisher_->initialize()) {
            handleError("初始化推流器失败");
            return false;
        }
    }

    Logger::getInstance().info( "录制管理器初始化成功");
    return true;
}

bool RecordManager::start() {
    std::lock_guard<std::mutex> lock(mutex_);

    if (running_.load()) {
        Logger::getInstance().warn( "录制管理器已在运行中");
        return true;
    }

    status_.store(RecordStatus::STARTING);

    // 生成第一个录制文件名
    current_file_ = generateFileName();

    // 开始录制
    if (!recorder_->startRecording(current_file_)) {
        handleError("开始录制失败");
        status_.store(RecordStatus::ERROR);
        return false;
    }

    // 开始推流（如果启用）
    if (publisher_ && !publisher_->startStreaming()) {
        Logger::getInstance().warn( "推流启动失败，但录制将继续");
    }

    running_.store(true);
    paused_.store(false);
    segment_start_time_ = std::chrono::steady_clock::now();
    total_start_time_ = segment_start_time_;
    retry_count_ = 0;

    // 启动管理线程
    manager_thread_ = std::thread(&RecordManager::managerThread, this);

    status_.store(RecordStatus::RECORDING);
    Logger::getInstance().info( "录制和推流已启动，文件: " + current_file_);

    return true;
}

void RecordManager::stop() {
    if (!running_.load()) {
        return;
    }

    status_.store(RecordStatus::STOPPING);
    running_.store(false);

    // 等待管理线程结束
    if (manager_thread_.joinable()) {
        manager_thread_.join();
    }

    // 停止录制和推流
    if (recorder_) {
        recorder_->stopRecording();
    }

    if (publisher_) {
        publisher_->stopStreaming();
    }

    // 添加当前文件到已录制列表
    if (!current_file_.empty()) {
        recorded_files_.push_back(current_file_);
        current_file_.clear();
    }

    status_.store(RecordStatus::STOPPED);
    Logger::getInstance().info( "录制和推流已停止");
}

void RecordManager::pause() {
    if (!running_.load() || paused_.load()) {
        return;
    }

    paused_.store(true);

    // 停止录制但保持推流
    if (recorder_) {
        recorder_->stopRecording();
    }

    Logger::getInstance().info( "录制已暂停，推流继续");
}

void RecordManager::resume() {
    if (!running_.load() || !paused_.load()) {
        return;
    }

    // 生成新的录制文件
    current_file_ = generateFileName();

    // 恢复录制
    if (recorder_ && recorder_->startRecording(current_file_)) {
        paused_.store(false);
        segment_start_time_ = std::chrono::steady_clock::now();
        Logger::getInstance().info( "录制已恢复，新文件: " + current_file_);
    } else {
        handleError("恢复录制失败");
    }
}

void RecordManager::getCurrentFileInfo(std::string& current_file, double& file_duration, double& total_duration) const {
    std::lock_guard<std::mutex> lock(mutex_);

    current_file = current_file_;

    if (recorder_) {
        file_duration = recorder_->getRecordingDuration();
    } else {
        file_duration = 0.0;
    }

    auto now = std::chrono::steady_clock::now();
    auto total_elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - total_start_time_);
    total_duration = total_elapsed.count();
}

void RecordManager::getStreamInfo(bool& is_streaming, int64_t& frames_sent, int64_t& bytes_sent, double& stream_duration) const {
    if (publisher_) {
        is_streaming = publisher_->isStreaming();
        publisher_->getStreamStats(frames_sent, bytes_sent, stream_duration);
    } else {
        is_streaming = false;
        frames_sent = 0;
        bytes_sent = 0;
        stream_duration = 0.0;
    }
}

void RecordManager::setStreamQuality(int quality) {
    if (publisher_) {
        publisher_->setStreamQuality(quality);
        Logger::getInstance().info( "推流质量已调整为: " + std::to_string(quality) + "/10");
    }
}

std::vector<std::string> RecordManager::getRecordedFiles() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return recorded_files_;
}

void RecordManager::setErrorCallback(std::function<void(const std::string&)> callback) {
    error_callback_ = callback;
}

void RecordManager::managerThread() {
    Logger::getInstance().info( "管理线程已启动");

    while (running_.load()) {
        try {
            // 检查是否需要切换文件
            if (!paused_.load() && shouldSwitchFile()) {
                if (!switchToNewFile()) {
                    handleError("切换录制文件失败");
                    if (config_.auto_restart && retry_count_ < config_.max_retry_count) {
                        retry_count_++;
                        Logger::getInstance().warn( "尝试重启录制，重试次数: " + std::to_string(retry_count_));
                        std::this_thread::sleep_for(std::chrono::seconds(5));
                        continue;
                    } else {
                        break;
                    }
                }
                retry_count_ = 0; // 重置重试计数
            }

            // 如果有推流器，检查推流状态并同步帧数据
            if (publisher_ && publisher_->isStreaming() && recorder_ && !paused_.load()) {
                AVFrame* frame = nullptr;
                if (recorder_->captureFrame(frame) && frame) {
                    publisher_->pushFrame(frame);
                }
            }

            // 短暂休眠以避免过度占用CPU
            std::this_thread::sleep_for(std::chrono::milliseconds(100));

        } catch (const std::exception& e) {
            handleError("管理线程异常: " + std::string(e.what()));
            break;
        }
    }

    Logger::getInstance().info( "管理线程已结束");
}

std::string RecordManager::generateFileName() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);

    std::ostringstream oss;
    oss << config_.output_dir << "/"
        << config_.file_prefix << "_"
        << std::put_time(&tm, "%Y%m%d_%H%M%S")
        << ".mp4";

    return oss.str();
}

bool RecordManager::shouldSwitchFile() const {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - segment_start_time_);
    return elapsed.count() >= config_.segment_duration;
}

bool RecordManager::switchToNewFile() {
    Logger::getInstance().info( "切换到新的录制文件");

    // 停止当前录制
    if (recorder_) {
        recorder_->stopRecording();
    }

    // 添加当前文件到已录制列表
    if (!current_file_.empty()) {
        recorded_files_.push_back(current_file_);
    }

    // 生成新文件名
    current_file_ = generateFileName();

    // 开始新的录制
    if (recorder_ && recorder_->startRecording(current_file_)) {
        segment_start_time_ = std::chrono::steady_clock::now();
        Logger::getInstance().info( "新录制文件已启动: " + current_file_);
        return true;
    } else {
        handleError("启动新录制文件失败: " + current_file_);
        return false;
    }
}

bool RecordManager::createOutputDirectory() const {
    try {
        std::filesystem::create_directories(config_.output_dir);
        return true;
    } catch (const std::exception& e) {
        Logger::getInstance().error(std::string("创建目录失败: ") + e.what());
        return false;
    }
}

void RecordManager::handleError(const std::string& error_msg) {
    Logger::getInstance().error( error_msg);

    if (error_callback_) {
        error_callback_(error_msg);
    }

    status_.store(RecordStatus::ERROR);
}

void RecordManager::cleanup() {
    recorder_.reset();
    publisher_.reset();
    recorded_files_.clear();
    current_file_.clear();
}
