#!/bin/bash

# 屏幕录制和推流工具构建脚本
# 作者: RecordTool Team
# 版本: 1.0

echo "=== 屏幕录制和推流工具构建脚本 ==="

# 检查依赖
echo "检查系统依赖..."

# 检查FFmpeg开发库
if ! pkg-config --exists libavcodec libavformat libavutil libavdevice libavfilter libswscale libswresample; then
    echo "错误: 未找到FFmpeg开发库"
    echo "请安装FFmpeg开发包:"
    echo "  Ubuntu/Debian: sudo apt-get install libavcodec-dev libavformat-dev libavutil-dev libavdevice-dev libavfilter-dev libswscale-dev libswresample-dev"
    echo "  CentOS/RHEL: sudo yum install ffmpeg-devel"
    echo "  Fedora: sudo dnf install ffmpeg-devel"
    exit 1
fi

# 检查X11开发库
if ! pkg-config --exists x11 xext xfixes; then
    echo "错误: 未找到X11开发库"
    echo "请安装X11开发包:"
    echo "  Ubuntu/Debian: sudo apt-get install libx11-dev libxext-dev libxfixes-dev"
    echo "  CentOS/RHEL: sudo yum install libX11-devel libXext-devel libXfixes-devel"
    echo "  Fedora: sudo dnf install libX11-devel libXext-devel libXfixes-devel"
    exit 1
fi

# 检查CMake
if ! command -v cmake &> /dev/null; then
    echo "错误: 未找到CMake"
    echo "请安装CMake:"
    echo "  Ubuntu/Debian: sudo apt-get install cmake"
    echo "  CentOS/RHEL: sudo yum install cmake"
    echo "  Fedora: sudo dnf install cmake"
    exit 1
fi

echo "依赖检查完成"

# 创建构建目录
BUILD_DIR="build"
if [ -d "$BUILD_DIR" ]; then
    echo "清理旧的构建目录..."
    rm -rf "$BUILD_DIR"
fi

echo "创建构建目录..."
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# 配置项目
echo "配置项目..."
cmake .. -DCMAKE_BUILD_TYPE=Release

if [ $? -ne 0 ]; then
    echo "错误: CMake配置失败"
    exit 1
fi

# 编译项目
echo "编译项目..."
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "错误: 编译失败"
    exit 1
fi

echo "构建成功!"
echo "可执行文件位置: $BUILD_DIR/RecordTool"

# 创建录制目录
RECORD_DIR="../recordings"
if [ ! -d "$RECORD_DIR" ]; then
    echo "创建录制目录: $RECORD_DIR"
    mkdir -p "$RECORD_DIR"
fi

echo ""
echo "=== 使用说明 ==="
echo "1. 基本录制（仅录制到文件）:"
echo "   ./RecordTool"
echo ""
echo "2. 录制并推流到RTMP服务器:"
echo "   ./RecordTool -r rtmp://your-server.com/live/stream_key"
echo ""
echo "3. 自定义录制参数:"
echo "   ./RecordTool -o /path/to/output -d 600 -w 1280 -h 720 -f 25"
echo ""
echo "4. 查看完整帮助:"
echo "   ./RecordTool --help"
echo ""
echo "注意: 运行前请确保X11服务器正在运行，并且有屏幕显示权限"
