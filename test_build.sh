#!/bin/bash

# 简化的测试构建脚本
echo "=== 测试构建脚本 ==="

# 检查基本工具
echo "检查编译工具..."
if ! command -v g++ &> /dev/null; then
    echo "错误: 未找到g++编译器"
    exit 1
fi

if ! command -v cmake &> /dev/null; then
    echo "错误: 未找到CMake"
    exit 1
fi

echo "编译工具检查完成"

# 检查FFmpeg库（如果没有，给出安装提示）
echo "检查FFmpeg库..."
if ! pkg-config --exists libavcodec 2>/dev/null; then
    echo "警告: 未找到FFmpeg开发库"
    echo "请安装FFmpeg开发包:"
    echo "  Ubuntu/Debian: sudo apt-get install libavcodec-dev libavformat-dev libavutil-dev libavdevice-dev libavfilter-dev libswscale-dev libswresample-dev"
    echo "  CentOS/RHEL: sudo yum install ffmpeg-devel"
    echo "  Fedora: sudo dnf install ffmpeg-devel"
    echo ""
    echo "如果您已经安装了FFmpeg但仍然看到此消息，请检查pkg-config路径设置"
    echo "继续尝试构建..."
fi

# 检查X11库
echo "检查X11库..."
if ! pkg-config --exists x11 2>/dev/null; then
    echo "警告: 未找到X11开发库"
    echo "请安装X11开发包:"
    echo "  Ubuntu/Debian: sudo apt-get install libx11-dev libxext-dev libxfixes-dev"
    echo "  CentOS/RHEL: sudo yum install libX11-devel libXext-devel libXfixes-devel"
    echo "  Fedora: sudo dnf install libX11-devel libXext-devel libXfixes-devel"
    echo ""
    echo "继续尝试构建..."
fi

# 创建构建目录
BUILD_DIR="build"
if [ -d "$BUILD_DIR" ]; then
    echo "清理旧的构建目录..."
    rm -rf "$BUILD_DIR"
fi

echo "创建构建目录..."
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# 尝试配置项目
echo "配置项目..."
if cmake .. -DCMAKE_BUILD_TYPE=Release; then
    echo "CMake配置成功"
else
    echo "CMake配置失败，尝试查看详细错误信息..."
    echo "可能的原因："
    echo "1. 缺少FFmpeg开发库"
    echo "2. 缺少X11开发库"
    echo "3. CMake版本过低"
    exit 1
fi

# 尝试编译
echo "开始编译..."
if make -j$(nproc); then
    echo "编译成功!"
    echo "可执行文件: $PWD/RecordTool"
    
    # 创建录制目录
    RECORD_DIR="../recordings"
    if [ ! -d "$RECORD_DIR" ]; then
        echo "创建录制目录: $RECORD_DIR"
        mkdir -p "$RECORD_DIR"
    fi
    
    echo ""
    echo "=== 测试运行 ==="
    echo "运行帮助命令测试..."
    if ./RecordTool --help; then
        echo "程序运行正常!"
    else
        echo "程序运行时出现问题，可能缺少运行时库"
    fi
    
else
    echo "编译失败"
    echo "请检查错误信息并安装缺少的依赖库"
    exit 1
fi

echo ""
echo "=== 构建完成 ==="
