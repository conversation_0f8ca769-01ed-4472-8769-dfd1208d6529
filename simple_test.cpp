// 简单的FFmpeg录制测试
#include <iostream>
#include <thread>
#include <chrono>

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/avutil.h>
#include <libavutil/opt.h>
#include <libavutil/imgutils.h>
}

// 辅助函数：获取FFmpeg错误字符串
static std::string av_error_string(int error_code) {
    char errbuf[AV_ERROR_MAX_STRING_SIZE];
    av_strerror(error_code, errbuf, AV_ERROR_MAX_STRING_SIZE);
    return std::string(errbuf);
}

int main() {
    std::cout << "=== 简单FFmpeg录制测试 ===" << std::endl;
    
    const char* filename = "../recordings/simple_test.mp4";
    const int width = 640;
    const int height = 480;
    const int fps = 30;
    const int duration_seconds = 5;
    
    // 初始化FFmpeg
    av_log_set_level(AV_LOG_WARNING);
    
    // 创建输出格式上下文
    AVFormatContext* format_ctx = nullptr;
    int ret = avformat_alloc_output_context2(&format_ctx, nullptr, nullptr, filename);
    if (ret < 0) {
        std::cerr << "创建输出格式上下文失败: " << av_error_string(ret) << std::endl;
        return 1;
    }
    
    // 查找编码器
    const AVCodec* codec = avcodec_find_encoder(AV_CODEC_ID_H264);
    if (!codec) {
        std::cerr << "找不到H264编码器" << std::endl;
        return 1;
    }
    
    // 创建编码器上下文
    AVCodecContext* codec_ctx = avcodec_alloc_context3(codec);
    if (!codec_ctx) {
        std::cerr << "分配编码器上下文失败" << std::endl;
        return 1;
    }
    
    // 设置编码器参数
    codec_ctx->bit_rate = 1000000;
    codec_ctx->width = width;
    codec_ctx->height = height;
    codec_ctx->time_base = {1, fps};
    codec_ctx->framerate = {fps, 1};
    codec_ctx->gop_size = fps;
    codec_ctx->max_b_frames = 0;
    codec_ctx->pix_fmt = AV_PIX_FMT_YUV420P;
    
    // 设置编码选项
    av_opt_set(codec_ctx->priv_data, "preset", "ultrafast", 0);
    av_opt_set(codec_ctx->priv_data, "crf", "23", 0);
    
    // 打开编码器
    ret = avcodec_open2(codec_ctx, codec, nullptr);
    if (ret < 0) {
        std::cerr << "打开编码器失败: " << av_error_string(ret) << std::endl;
        return 1;
    }
    
    // 创建视频流
    AVStream* stream = avformat_new_stream(format_ctx, nullptr);
    if (!stream) {
        std::cerr << "创建视频流失败" << std::endl;
        return 1;
    }
    
    // 复制编码器参数到流
    ret = avcodec_parameters_from_context(stream->codecpar, codec_ctx);
    if (ret < 0) {
        std::cerr << "复制编码器参数失败: " << av_error_string(ret) << std::endl;
        return 1;
    }
    
    stream->time_base = codec_ctx->time_base;
    
    // 打开输出文件
    ret = avio_open(&format_ctx->pb, filename, AVIO_FLAG_WRITE);
    if (ret < 0) {
        std::cerr << "打开输出文件失败: " << av_error_string(ret) << std::endl;
        return 1;
    }
    
    // 写入文件头
    ret = avformat_write_header(format_ctx, nullptr);
    if (ret < 0) {
        std::cerr << "写入文件头失败: " << av_error_string(ret) << std::endl;
        return 1;
    }
    
    // 分配帧
    AVFrame* frame = av_frame_alloc();
    if (!frame) {
        std::cerr << "分配帧失败" << std::endl;
        return 1;
    }
    
    frame->format = codec_ctx->pix_fmt;
    frame->width = width;
    frame->height = height;
    
    ret = av_frame_get_buffer(frame, 32);
    if (ret < 0) {
        std::cerr << "分配帧缓冲区失败: " << av_error_string(ret) << std::endl;
        return 1;
    }
    
    // 分配数据包
    AVPacket* packet = av_packet_alloc();
    if (!packet) {
        std::cerr << "分配数据包失败" << std::endl;
        return 1;
    }
    
    std::cout << "开始录制 " << duration_seconds << " 秒..." << std::endl;
    
    // 录制循环
    int total_frames = fps * duration_seconds;
    for (int i = 0; i < total_frames; i++) {
        // 确保帧可写
        ret = av_frame_make_writable(frame);
        if (ret < 0) {
            std::cerr << "无法使帧可写: " << av_error_string(ret) << std::endl;
            break;
        }
        
        // 生成测试图像
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                frame->data[0][y * frame->linesize[0] + x] = (x + y + i) % 256;
            }
        }
        
        // 色度分量
        for (int y = 0; y < height / 2; y++) {
            for (int x = 0; x < width / 2; x++) {
                frame->data[1][y * frame->linesize[1] + x] = 128;
                frame->data[2][y * frame->linesize[2] + x] = 128;
            }
        }
        
        frame->pts = i;
        
        // 编码帧
        ret = avcodec_send_frame(codec_ctx, frame);
        if (ret < 0) {
            std::cerr << "发送帧到编码器失败: " << av_error_string(ret) << std::endl;
            break;
        }
        
        while (ret >= 0) {
            ret = avcodec_receive_packet(codec_ctx, packet);
            if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
                break;
            } else if (ret < 0) {
                std::cerr << "接收编码数据包失败: " << av_error_string(ret) << std::endl;
                goto cleanup;
            }
            
            packet->stream_index = stream->index;
            av_packet_rescale_ts(packet, codec_ctx->time_base, stream->time_base);
            
            ret = av_interleaved_write_frame(format_ctx, packet);
            av_packet_unref(packet);
            
            if (ret < 0) {
                std::cerr << "写入数据包失败: " << av_error_string(ret) << std::endl;
                goto cleanup;
            }
        }
        
        if (i % fps == 0) {
            std::cout << "已录制 " << (i / fps + 1) << " 秒" << std::endl;
        }
        
        // 控制帧率
        std::this_thread::sleep_for(std::chrono::milliseconds(1000 / fps));
    }
    
    // 刷新编码器
    avcodec_send_frame(codec_ctx, nullptr);
    while (true) {
        ret = avcodec_receive_packet(codec_ctx, packet);
        if (ret == AVERROR_EOF) {
            break;
        } else if (ret < 0) {
            break;
        }
        
        packet->stream_index = stream->index;
        av_packet_rescale_ts(packet, codec_ctx->time_base, stream->time_base);
        av_interleaved_write_frame(format_ctx, packet);
        av_packet_unref(packet);
    }
    
    // 写入文件尾
    av_write_trailer(format_ctx);
    
    std::cout << "录制完成！文件保存为: " << filename << std::endl;
    
cleanup:
    // 清理资源
    av_frame_free(&frame);
    av_packet_free(&packet);
    avcodec_free_context(&codec_ctx);
    avio_closep(&format_ctx->pb);
    avformat_free_context(format_ctx);
    
    return 0;
}
