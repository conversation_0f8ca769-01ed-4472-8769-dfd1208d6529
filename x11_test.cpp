// 简单的X11屏幕捕获测试
#include <iostream>
#include <X11/Xlib.h>
#include <X11/Xutil.h>

int main() {
    std::cout << "=== X11屏幕捕获测试 ===" << std::endl;
    
    // 打开显示器
    Display* display = XOpenDisplay(":0");
    if (!display) {
        std::cerr << "无法打开X11显示器" << std::endl;
        return 1;
    }
    
    // 获取屏幕信息
    int screen = DefaultScreen(display);
    Window root = RootWindow(display, screen);
    int screen_width = DisplayWidth(display, screen);
    int screen_height = DisplayHeight(display, screen);
    
    std::cout << "屏幕尺寸: " << screen_width << "x" << screen_height << std::endl;
    
    // 测试小尺寸捕获
    int test_width = 100;
    int test_height = 100;
    
    std::cout << "尝试捕获 " << test_width << "x" << test_height << " 区域..." << std::endl;
    
    XImage* image = XGetImage(display, root, 0, 0, test_width, test_height, AllPlanes, ZPixmap);
    if (!image) {
        std::cerr << "XGetImage失败" << std::endl;
        XCloseDisplay(display);
        return 1;
    }
    
    std::cout << "成功捕获图像!" << std::endl;
    std::cout << "图像信息:" << std::endl;
    std::cout << "  宽度: " << image->width << std::endl;
    std::cout << "  高度: " << image->height << std::endl;
    std::cout << "  深度: " << image->depth << std::endl;
    std::cout << "  每像素位数: " << image->bits_per_pixel << std::endl;
    std::cout << "  字节序: " << (image->byte_order == LSBFirst ? "LSB" : "MSB") << std::endl;
    
    // 测试读取几个像素
    std::cout << "前几个像素值:" << std::endl;
    for (int y = 0; y < std::min(3, image->height); y++) {
        for (int x = 0; x < std::min(3, image->width); x++) {
            unsigned long pixel = XGetPixel(image, x, y);
            std::cout << "  (" << x << "," << y << "): 0x" << std::hex << pixel << std::dec << std::endl;
        }
    }
    
    // 清理
    XDestroyImage(image);
    XCloseDisplay(display);
    
    std::cout << "X11测试完成!" << std::endl;
    return 0;
}
